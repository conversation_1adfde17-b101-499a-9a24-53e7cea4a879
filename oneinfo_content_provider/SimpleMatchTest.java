import com.jidou.cariad.content.common.utils.StringMatchUtil;

/**
 * 简单的匹配测试类
 * 用于验证改进的字符串匹配算法
 */
public class SimpleMatchTest {
    
    public static void main(String[] args) {
        System.out.println("=== 字符串匹配算法测试 ===");
        
        // 测试1: 兜底阈值机制 - 防止不相关匹配
        System.out.println("\n1. 测试兜底阈值机制:");
        testCase("胡杨河市", "上海市/杨浦区", "应该返回0，防止不相关匹配");
        testCase("深圳", "哈尔滨市/南岗区", "应该返回0，防止不相关匹配");
        testCase("abc", "xyz", "完全无重叠字符应该返回0");
        
        // 测试2: 正常的城市匹配
        System.out.println("\n2. 测试正常城市匹配:");
        testCase("武汉", "湖北省/武汉市", "正常城市匹配应该有合理得分");
        testCase("上海", "上海市", "精确匹配应该得分最高");
        testCase("北京", "北京市/朝阳区", "部分匹配应该有合理得分");
        
        // 测试3: 具体匹配优于通用匹配
        System.out.println("\n3. 测试具体匹配优于通用匹配:");
        double generalScore = StringMatchUtil.calculateMatchScore("武汉江汉", "湖北省/武汉市");
        double specificScore = StringMatchUtil.calculateMatchScore("武汉江汉", "湖北省/武汉市/江汉区");
        System.out.printf("武汉江汉 vs 湖北省/武汉市: %.2f\n", generalScore);
        System.out.printf("武汉江汉 vs 湖北省/武汉市/江汉区: %.2f\n", specificScore);
        System.out.printf("具体匹配是否优于通用匹配: %s\n", specificScore > generalScore ? "是" : "否");
        
        // 测试4: 连续匹配奖励
        System.out.println("\n4. 测试连续匹配奖励:");
        double consecutiveScore = StringMatchUtil.calculateMatchScore("武汉", "湖北省武汉市");
        double nonConsecutiveScore = StringMatchUtil.calculateMatchScore("武汉", "湖北省武江汉市");
        System.out.printf("武汉 vs 湖北省武汉市 (连续): %.2f\n", consecutiveScore);
        System.out.printf("武汉 vs 湖北省武江汉市 (非连续): %.2f\n", nonConsecutiveScore);
        System.out.printf("连续匹配是否得分更高: %s\n", consecutiveScore > nonConsecutiveScore ? "是" : "否");
        
        // 测试5: 长度适配性检查
        System.out.println("\n5. 测试长度适配性检查:");
        double shortScore = StringMatchUtil.calculateMatchScore("北京", "北京市");
        double longScore = StringMatchUtil.calculateMatchScore("北京", "北京市朝阳区建国门外大街甲6号");
        System.out.printf("北京 vs 北京市: %.2f\n", shortScore);
        System.out.printf("北京 vs 北京市朝阳区建国门外大街甲6号: %.2f\n", longScore);
        System.out.printf("长度差异过大是否降分: %s\n", shortScore > longScore ? "是" : "否");
        
        // 测试6: 位置权重机制
        System.out.println("\n6. 测试位置权重机制:");
        double frontScore = StringMatchUtil.calculateMatchScore("上海浦东", "上海市/浦东新区");
        double backScore = StringMatchUtil.calculateMatchScore("浦东上海", "上海市/浦东新区");
        System.out.printf("上海浦东 vs 上海市/浦东新区: %.2f\n", frontScore);
        System.out.printf("浦东上海 vs 上海市/浦东新区: %.2f\n", backScore);
        System.out.printf("匹配位置靠前是否得分更高: %s\n", frontScore >= backScore ? "是" : "否");
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    private static void testCase(String input, String target, String description) {
        double score = StringMatchUtil.calculateMatchScore(input, target);
        System.out.printf("%s vs %s: %.2f (%s)\n", input, target, score, description);
    }
}
