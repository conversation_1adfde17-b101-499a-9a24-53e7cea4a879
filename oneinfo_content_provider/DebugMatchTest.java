import com.jidou.cariad.content.common.utils.StringMatchUtil;

/**
 * 调试匹配测试类
 * 用于调试"胡杨河市" vs "上海市/杨浦区"的匹配问题
 */
public class DebugMatchTest {
    
    public static void main(String[] args) {
        String input = "胡杨河市";
        String target = "上海市/杨浦区";
        
        System.out.println("=== 调试匹配算法 ===");
        System.out.printf("输入: %s\n", input);
        System.out.printf("目标: %s\n", target);
        
        // 标准化字符串
        String normalizedInput = normalizeString(input);
        String normalizedTarget = normalizeString(target);
        System.out.printf("标准化输入: %s\n", normalizedInput);
        System.out.printf("标准化目标: %s\n", normalizedTarget);
        
        // 计算字符匹配比例
        double overlapRatio = StringMatchUtil.calculateCharacterMatchRatio(normalizedInput, normalizedTarget);
        int matchedCharCount = (int) (overlapRatio * normalizedInput.length());
        System.out.printf("字符重叠率: %.2f\n", overlapRatio);
        System.out.printf("匹配字符数量: %d\n", matchedCharCount);
        
        // 检查每个字符的匹配情况
        System.out.println("字符匹配详情:");
        for (int i = 0; i < normalizedInput.length(); i++) {
            char c = normalizedInput.charAt(i);
            boolean found = normalizedTarget.indexOf(c) >= 0;
            System.out.printf("  '%c': %s\n", c, found ? "匹配" : "不匹配");
        }
        
        // 核心地名词汇检查
        String coreInput = removeCoreLocationWords(normalizedInput);
        String coreTarget = removeCoreLocationWords(normalizedTarget);
        System.out.printf("移除通用词汇后的输入: '%s'\n", coreInput);
        System.out.printf("移除通用词汇后的目标: '%s'\n", coreTarget);

        double coreMatchRatio = StringMatchUtil.calculateCharacterMatchRatio(coreInput, coreTarget);
        System.out.printf("核心词汇匹配率: %.2f\n", coreMatchRatio);
        System.out.printf("是否通过核心地名检查: %s\n", coreMatchRatio > 0.3 ? "是" : "否");

        // 最终得分
        double finalScore = StringMatchUtil.calculateMatchScore(input, target);
        System.out.printf("最终得分: %.2f\n", finalScore);

        // 测试阈值
        System.out.printf("字符重叠率阈值: 0.5\n");
        System.out.printf("最低匹配字符数量阈值: 2\n");
        System.out.printf("是否通过字符重叠率检查: %s\n", overlapRatio >= 0.5 ? "是" : "否");
        System.out.printf("是否通过匹配字符数量检查: %s\n", matchedCharCount >= 2 ? "是" : "否");
    }

    private static String removeCoreLocationWords(String input) {
        String[] commonWords = {"市", "区", "县", "省", "镇", "街", "路", "村", "乡"};
        String result = input;
        for (String commonWord : commonWords) {
            result = result.replace(commonWord, "");
        }
        return result;
    }
    
    private static String normalizeString(String str) {
        if (str == null) {
            return "";
        }
        return str.trim().toLowerCase();
    }
}
