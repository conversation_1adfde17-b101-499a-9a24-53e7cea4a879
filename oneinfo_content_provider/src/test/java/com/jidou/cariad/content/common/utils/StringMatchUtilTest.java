package com.jidou.cariad.content.common.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * StringMatchUtil 单元测试
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
class StringMatchUtilTest {

    @Test
    @DisplayName("测试精确匹配")
    void testExactMatch() {
        double score = StringMatchUtil.calculateMatchScore("武汉", "武汉");
        assertEquals(100.0, score, 0.01);
    }

    @Test
    @DisplayName("测试部分匹配")
    void testPartialMatch() {
        // 武汉 vs 湖北省/武汉市 - 应该匹配度高但有长度惩罚
        double score1 = StringMatchUtil.calculateMatchScore("武汉", "湖北省/武汉市");

        // 武汉 vs 湖北省/武汉市/江汉区 - 长度惩罚更大
        double score2 = StringMatchUtil.calculateMatchScore("武汉", "湖北省/武汉市/江汉区");

        // 第一个得分应该高于第二个
        assertTrue(score1 > score2);
        assertTrue(score1 > 90); // 匹配度高
        assertTrue(score2 > 85); // 匹配度也不错但惩罚更多
    }

    @Test
    @DisplayName("测试具体匹配优于通用匹配")
    void testSpecificMatchBetterThanGeneral() {
        // 武汉江汉 vs 湖北省/武汉市 - 匹配度50%
        double generalScore = StringMatchUtil.calculateMatchScore("武汉江汉", "湖北省/武汉市");

        // 武汉江汉 vs 湖北省/武汉市/江汉区 - 匹配度100%
        double specificScore = StringMatchUtil.calculateMatchScore("武汉江汉", "湖北省/武汉市/江汉区");

        // 具体匹配应该得分更高
        assertTrue(specificScore > generalScore);
    }

    @Test
    @DisplayName("测试空值处理")
    void testNullAndEmptyHandling() {
        assertEquals(0.0, StringMatchUtil.calculateMatchScore(null, "test"));
        assertEquals(0.0, StringMatchUtil.calculateMatchScore("test", null));
        assertEquals(0.0, StringMatchUtil.calculateMatchScore("", "test"));
        assertEquals(0.0, StringMatchUtil.calculateMatchScore("test", ""));
        assertEquals(0.0, StringMatchUtil.calculateMatchScore(null, null));
    }

    @Test
    @DisplayName("测试字符匹配比例")
    void testCharacterMatchRatio() {
        // 武汉 在 湖北省/武汉市 中完全匹配
        double ratio1 = StringMatchUtil.calculateCharacterMatchRatio("武汉", "湖北省/武汉市");
        assertEquals(1.0, ratio1, 0.01);

        // 武汉江汉 在 湖北省/武汉市 中部分匹配
        double ratio2 = StringMatchUtil.calculateCharacterMatchRatio("武汉江汉", "湖北省/武汉市");
        assertEquals(0.5, ratio2, 0.01);
    }

    @Test
    @DisplayName("测试包含匹配")
    void testContainsScore() {
        // 武汉 包含在 湖北省/武汉市 中
        double score1 = StringMatchUtil.calculateContainsScore("武汉", "湖北省/武汉市");
        assertTrue(score1 > 0);

        // 不包含的情况
        double score2 = StringMatchUtil.calculateContainsScore("上海", "湖北省/武汉市");
        assertEquals(0.0, score2);
    }

    @Test
    @DisplayName("测试最长公共子序列")
    void testLongestCommonSubsequence() {
        int lcs1 = StringMatchUtil.calculateLongestCommonSubsequence("武汉", "湖北省/武汉市");
        assertEquals(2, lcs1); // "武汉" 两个字符都匹配

        int lcs2 = StringMatchUtil.calculateLongestCommonSubsequence("武汉江汉", "湖北省/武汉市/江汉区");
        assertEquals(4, lcs2); // "武汉江汉" 四个字符都匹配
    }

    @Test
    @DisplayName("测试自定义配置")
    void testCustomConfig() {
        StringMatchUtil.MatchConfig config = new StringMatchUtil.MatchConfig()
                .lengthPenalty(0.1) // 更高的长度惩罚
                .caseSensitive(false)
                .ignoreSpaces(true);

        double score = StringMatchUtil.calculateMatchScore("武汉", "湖北省/武汉市", config);
        assertTrue(score > 0);
        assertTrue(score < 100); // 应该有惩罚
    }

    @Test
    @DisplayName("测试大小写不敏感")
    void testCaseInsensitive() {
        double score1 = StringMatchUtil.calculateMatchScore("Beijing", "beijing");
        double score2 = StringMatchUtil.calculateMatchScore("BEIJING", "beijing");

        // 默认大小写不敏感，应该都是精确匹配
        assertEquals(100.0, score1, 0.01);
        assertEquals(100.0, score2, 0.01);
    }

    @Test
    @DisplayName("测试边界情况")
    void testEdgeCases() {
        // 单字符匹配
        double score1 = StringMatchUtil.calculateMatchScore("武", "武汉");
        assertTrue(score1 > 90);

        // 完全不匹配
        double score2 = StringMatchUtil.calculateMatchScore("上海", "北京");
        assertEquals(0.0, score2);

        // 长字符串匹配
        String longInput = "湖北省武汉市江汉区";
        String longTarget = "湖北省武汉市江汉区人民政府";
        double score3 = StringMatchUtil.calculateMatchScore(longInput, longTarget);
        assertTrue(score3 > 80); // 应该有较高匹配度
    }

    @Test
    @DisplayName("测试兜底阈值机制 - 防止不相关匹配")
    void testMinimumThresholdPreventsIrrelevantMatches() {
        // 测试"胡杨河市"不应该匹配"上海市/杨浦区"
        double score1 = StringMatchUtil.calculateMatchScore("胡杨河市", "上海市/杨浦区");
        assertEquals(0.0, score1, "胡杨河市不应该匹配上海市/杨浦区");

        // 测试其他不相关的匹配
        double score2 = StringMatchUtil.calculateMatchScore("深圳", "哈尔滨市/南岗区");
        assertEquals(0.0, score2, "深圳不应该匹配哈尔滨");

        // 测试字符重叠率过低的情况
        double score3 = StringMatchUtil.calculateMatchScore("abc", "xyz");
        assertEquals(0.0, score3, "完全无重叠字符应该返回0");
    }

    @Test
    @DisplayName("测试连续匹配奖励机制")
    void testConsecutiveMatchBonus() {
        // 连续匹配应该得分更高
        double score1 = StringMatchUtil.calculateMatchScore("武汉", "湖北省武汉市");
        double score2 = StringMatchUtil.calculateMatchScore("武汉", "湖北省武江汉市"); // 非连续

        assertTrue(score1 > score2, "连续匹配应该得分更高");
    }

    @Test
    @DisplayName("测试长度适配性检查")
    void testLengthAdaptabilityCheck() {
        // 长度差异过大应该大幅降分
        double score1 = StringMatchUtil.calculateMatchScore("北京", "北京市");
        double score2 = StringMatchUtil.calculateMatchScore("北京", "北京市朝阳区建国门外大街甲6号");

        assertTrue(score1 > score2, "长度差异过大应该降分");

        // 长度差异超过2倍的情况
        double score3 = StringMatchUtil.calculateMatchScore("上海", "上海市浦东新区陆家嘴金融贸易区");
        assertTrue(score3 < score1, "长度差异超过2倍应该大幅降分");
    }

    @Test
    @DisplayName("测试位置权重机制")
    void testPositionWeightMechanism() {
        // 匹配位置靠前应该得分更高
        double score1 = StringMatchUtil.calculateMatchScore("上海浦东", "上海市/浦东新区");
        double score2 = StringMatchUtil.calculateMatchScore("浦东上海", "上海市/浦东新区");

        // 由于"上海"在目标字符串中位置更靠前，第一个应该得分更高
        assertTrue(score1 >= score2, "匹配位置靠前应该得分更高或相等");
    }

    @Test
    @DisplayName("测试改进算法的实际场景")
    void testRealWorldScenarios() {
        // 测试正常的城市匹配
        double score1 = StringMatchUtil.calculateMatchScore("武汉", "湖北省/武汉市");
        assertTrue(score1 > 50, "正常城市匹配应该有合理得分");

        // 测试更具体的匹配优于通用匹配
        double generalScore = StringMatchUtil.calculateMatchScore("武汉江汉", "湖北省/武汉市");
        double specificScore = StringMatchUtil.calculateMatchScore("武汉江汉", "湖北省/武汉市/江汉区");
        assertTrue(specificScore > generalScore, "具体匹配应该优于通用匹配");

        // 测试部分匹配的合理性
        double partialScore = StringMatchUtil.calculateMatchScore("上海浦东", "上海市/浦东新区");
        assertTrue(partialScore > 60, "合理的部分匹配应该有较高得分");
    }
}
