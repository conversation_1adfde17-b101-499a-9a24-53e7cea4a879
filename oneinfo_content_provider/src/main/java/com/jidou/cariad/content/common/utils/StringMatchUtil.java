package com.jidou.cariad.content.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 字符串匹配工具类
 * 提供多种字符串匹配算法和评分策略
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public final class StringMatchUtil {

    /**
     * 默认长度惩罚系数
     */
    private static final double DEFAULT_LENGTH_PENALTY = 0.05;

    /**
     * 精确匹配得分
     */
    private static final double EXACT_MATCH_SCORE = 100.0;

    /**
     * 最低匹配阈值 - 低于此分数视为无效匹配
     */
    private static final double MIN_MATCH_THRESHOLD = 30.0;

    /**
     * 最低字符重叠率阈值
     */
    private static final double MIN_OVERLAP_RATIO = 0.4;

    /**
     * 连续匹配奖励系数
     */
    private static final double CONSECUTIVE_MATCH_BONUS = 1.5;

    /**
     * 位置权重衰减系数
     */
    private static final double POSITION_WEIGHT_DECAY = 0.9;

    /**
     * 私有构造函数，防止实例化
     */
    private StringMatchUtil() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * 计算字符串匹配得分
     * 综合考虑字符匹配度和长度适配性
     *
     * @param input  输入字符串
     * @param target 目标字符串
     * @return 匹配得分（0.0-100.0），越高表示越匹配
     */
    public static double calculateMatchScore(String input, String target) {
        return calculateMatchScore(input, target, DEFAULT_LENGTH_PENALTY);
    }

    /**
     * 计算字符串匹配得分（改进版）
     * 使用多层过滤机制和改进的评分算法
     *
     * @param input         输入字符串
     * @param target        目标字符串
     * @param lengthPenalty 长度惩罚系数，每个字符差异的扣分
     * @return 匹配得分（0.0-100.0），越高表示越匹配
     */
    public static double calculateMatchScore(String input, String target, double lengthPenalty) {
        if (StringUtils.isBlank(input) || StringUtils.isBlank(target)) {
            return 0.0;
        }

        String normalizedInput = normalizeString(input);
        String normalizedTarget = normalizeString(target);

        // 精确匹配得分最高
        if (normalizedTarget.equals(normalizedInput)) {
            return EXACT_MATCH_SCORE;
        }

        // 第一层过滤：基础字符重叠率检查
        double basicOverlapRatio = calculateCharacterMatchRatio(normalizedInput, normalizedTarget);
        if (basicOverlapRatio < MIN_OVERLAP_RATIO) {
            return 0.0; // 字符重叠率过低，直接返回0
        }

        // 第二层评分：计算改进的匹配得分
        double enhancedScore = calculateEnhancedMatchScore(normalizedInput, normalizedTarget);

        // 第三层过滤：长度适配性检查
        double lengthRatio = calculateLengthRatio(normalizedInput, normalizedTarget);
        if (lengthRatio > 2.0) { // 长度差异超过2倍，大幅降分
            enhancedScore *= 0.3;
        } else if (lengthRatio > 1.5) { // 长度差异超过1.5倍，适度降分
            enhancedScore *= 0.6;
        }

        // 应用长度惩罚
        int lengthDiff = Math.abs(normalizedTarget.length() - normalizedInput.length());
        double penalty = lengthDiff * lengthPenalty;
        enhancedScore = Math.max(0.0, enhancedScore - penalty);

        // 第四层过滤：最低阈值检查
        if (enhancedScore < MIN_MATCH_THRESHOLD) {
            return 0.0;
        }

        return Math.min(100.0, enhancedScore);
    }

    /**
     * 计算改进的匹配得分
     * 综合考虑连续匹配、位置权重和字符重叠度
     *
     * @param input  输入字符串（已标准化）
     * @param target 目标字符串（已标准化）
     * @return 改进的匹配得分
     */
    private static double calculateEnhancedMatchScore(String input, String target) {
        // 基础字符匹配得分
        double baseScore = calculateCharacterMatchRatio(input, target) * 100;

        // 连续匹配奖励
        double consecutiveBonus = calculateConsecutiveMatchBonus(input, target);

        // 位置权重得分
        double positionScore = calculatePositionWeightedScore(input, target);

        // 综合得分
        return baseScore + consecutiveBonus + positionScore;
    }

    /**
     * 计算连续匹配奖励
     * 连续匹配的字符序列给予额外加分
     *
     * @param input  输入字符串
     * @param target 目标字符串
     * @return 连续匹配奖励分数
     */
    private static double calculateConsecutiveMatchBonus(String input, String target) {
        double bonus = 0.0;
        int maxConsecutive = 0;
        int currentConsecutive = 0;

        for (int i = 0; i < input.length(); i++) {
            boolean found = false;
            for (int j = 0; j < target.length(); j++) {
                if (input.charAt(i) == target.charAt(j)) {
                    // 检查是否为连续匹配
                    if (i > 0 && j > 0 &&
                        input.charAt(i - 1) == target.charAt(j - 1)) {
                        currentConsecutive++;
                    } else {
                        currentConsecutive = 1;
                    }
                    maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
                    found = true;
                    break;
                }
            }
            if (!found) {
                currentConsecutive = 0;
            }
        }

        // 连续匹配长度越长，奖励越高
        if (maxConsecutive >= 2) {
            bonus = maxConsecutive * CONSECUTIVE_MATCH_BONUS;
        }

        return bonus;
    }

    /**
     * 计算位置权重得分
     * 匹配位置越靠前权重越高
     *
     * @param input  输入字符串
     * @param target 目标字符串
     * @return 位置权重得分
     */
    private static double calculatePositionWeightedScore(String input, String target) {
        double score = 0.0;

        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            int firstIndex = target.indexOf(c);
            if (firstIndex >= 0) {
                // 位置越靠前权重越高
                double positionWeight = Math.pow(POSITION_WEIGHT_DECAY, firstIndex);
                score += positionWeight;
            }
        }

        return score;
    }

    /**
     * 计算长度比例
     *
     * @param input  输入字符串
     * @param target 目标字符串
     * @return 长度比例（较长字符串长度 / 较短字符串长度）
     */
    private static double calculateLengthRatio(String input, String target) {
        int inputLen = input.length();
        int targetLen = target.length();
        return (double) Math.max(inputLen, targetLen) / Math.min(inputLen, targetLen);
    }

    /**
     * 计算字符匹配比例
     * 计算输入字符串中有多少字符在目标字符串中存在
     *
     * @param input  输入字符串（已标准化）
     * @param target 目标字符串（已标准化）
     * @return 字符匹配比例（0.0-1.0）
     */
    public static double calculateCharacterMatchRatio(String input, String target) {
        if (StringUtils.isBlank(input) || StringUtils.isBlank(target)) {
            return 0.0;
        }

        int matchCount = 0;
        for (char c : input.toCharArray()) {
            if (target.indexOf(c) >= 0) {
                matchCount++;
            }
        }

        return (double) matchCount / input.length();
    }

    /**
     * 计算包含匹配得分
     * 检查目标字符串是否包含输入字符串
     *
     * @param input  输入字符串
     * @param target 目标字符串
     * @return 如果目标包含输入则返回高分，否则返回0
     */
    public static double calculateContainsScore(String input, String target) {
        if (StringUtils.isBlank(input) || StringUtils.isBlank(target)) {
            return 0.0;
        }

        String normalizedInput = normalizeString(input);
        String normalizedTarget = normalizeString(target);

        if (normalizedTarget.contains(normalizedInput)) {
            // 包含匹配，长度越接近得分越高
            double lengthRatio = (double) normalizedInput.length() / normalizedTarget.length();
            return lengthRatio * 100;
        }

        return 0.0;
    }

    /**
     * 计算最长公共子序列长度
     * 使用动态规划算法
     *
     * @param input  输入字符串
     * @param target 目标字符串
     * @return 最长公共子序列长度
     */
    public static int calculateLongestCommonSubsequence(String input, String target) {
        if (StringUtils.isBlank(input) || StringUtils.isBlank(target)) {
            return 0;
        }

        String normalizedInput = normalizeString(input);
        String normalizedTarget = normalizeString(target);

        int m = normalizedInput.length();
        int n = normalizedTarget.length();

        // 创建DP表
        int[][] dp = new int[m + 1][n + 1];

        // 填充DP表
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (normalizedInput.charAt(i - 1) == normalizedTarget.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1] + 1;
                } else {
                    dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
                }
            }
        }

        return dp[m][n];
    }

    /**
     * 标准化字符串
     * 去除空格并转换为小写
     *
     * @param str 原始字符串
     * @return 标准化后的字符串
     */
    private static String normalizeString(String str) {
        if (str == null) {
            return "";
        }
        return str.trim().toLowerCase();
    }

    /**
     * 使用自定义配置计算匹配得分
     *
     * @param input  输入字符串
     * @param target 目标字符串
     * @param config 匹配配置
     * @return 匹配得分
     */
    public static double calculateMatchScore(String input, String target, MatchConfig config) {
        if (StringUtils.isBlank(input) || StringUtils.isBlank(target)) {
            return 0.0;
        }

        String processedInput = processString(input, config);
        String processedTarget = processString(target, config);

        // 精确匹配得分最高
        if (processedTarget.equals(processedInput)) {
            return EXACT_MATCH_SCORE;
        }

        // 计算字符匹配度
        double characterMatchRatio = calculateCharacterMatchRatio(processedInput, processedTarget);

        // 计算长度差异惩罚
        int lengthDiff = Math.abs(processedTarget.length() - processedInput.length());
        double penalty = lengthDiff * config.getLengthPenalty();

        return Math.max(0.0, characterMatchRatio * 100 - penalty);
    }

    /**
     * 根据配置处理字符串
     *
     * @param str    原始字符串
     * @param config 处理配置
     * @return 处理后的字符串
     */
    private static String processString(String str, MatchConfig config) {
        if (str == null) {
            return "";
        }

        String result = str;

        if (config.isIgnoreSpaces()) {
            result = result.trim();
        }

        if (!config.isCaseSensitive()) {
            result = result.toLowerCase();
        }

        return result;
    }

    /**
     * 匹配配置类
     * 用于自定义匹配参数
     */
    public static class MatchConfig {
        private double lengthPenalty = DEFAULT_LENGTH_PENALTY;
        private boolean caseSensitive = false;
        private boolean ignoreSpaces = true;

        public MatchConfig lengthPenalty(double lengthPenalty) {
            this.lengthPenalty = lengthPenalty;
            return this;
        }

        public MatchConfig caseSensitive(boolean caseSensitive) {
            this.caseSensitive = caseSensitive;
            return this;
        }

        public MatchConfig ignoreSpaces(boolean ignoreSpaces) {
            this.ignoreSpaces = ignoreSpaces;
            return this;
        }

        public double getLengthPenalty() {
            return lengthPenalty;
        }

        public boolean isCaseSensitive() {
            return caseSensitive;
        }

        public boolean isIgnoreSpaces() {
            return ignoreSpaces;
        }
    }
}
